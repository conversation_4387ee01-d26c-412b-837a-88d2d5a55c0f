<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D轮播效果演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }

        .description {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        /* 轮播容器样式 */
        .lead-carousel {
            position: relative;
            width: 100%;
            height: 180px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            margin: 20px auto;
            padding: 20px;
            perspective: 1000px;
            overflow: hidden;
        }

        .lead-scroll-container {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .lead-item {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 60px;
            margin-top: -30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(49, 130, 206, 0.05);
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            backface-visibility: hidden;
            transform-origin: center center;
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 2px 10px rgba(49, 130, 206, 0.1);
        }

        .lead-item.active {
            background: rgba(49, 130, 206, 0.1);
            color: #000;
            font-weight: 700;
            box-shadow: 0 4px 20px rgba(49, 130, 206, 0.2);
        }

        /* 指示器样式 */
        .indicators-container {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 8px;
            z-index: 10;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(239, 68, 68, 0.6);
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            outline: none;
        }

        .indicator.active {
            background: #4caf50;
            transform: scale(1.3);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.6);
        }

        .indicator.nav-button:hover {
            background: #dc2626;
            transform: scale(1.15);
            box-shadow: 0 2px 6px rgba(220, 38, 38, 0.5);
        }

        .controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .info {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 15px;
            margin-top: 30px;
            border-radius: 0 8px 8px 0;
        }

        .info h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 16px;
        }

        .info p {
            margin: 0;
            color: #4a5568;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="title">🎯 3D轮播效果演示</h1>
        <p class="description">
            现在使用顺畅的3D旋转效果，而不是之前突兀的切换移动感。<br>
            点击右侧的指示器按钮体验新的动画效果！
        </p>
        
        <!-- 轮播容器 -->
        <div class="lead-carousel" id="leadCarousel">
            <div class="lead-scroll-container">
                <div class="lead-item active">
                    <p>📞 联系方式 1<br>电话: 123-456-7890</p>
                </div>
                <div class="lead-item">
                    <p>📧 联系方式 2<br>邮箱: <EMAIL></p>
                </div>
                <div class="lead-item">
                    <p>📍 联系方式 3<br>地址: 北京市朝阳区</p>
                </div>
                <div class="lead-item">
                    <p>💬 联系方式 4<br>微信: example_wechat</p>
                </div>
            </div>
            
            <!-- 指示器 -->
            <div class="indicators-container">
                <div class="indicator nav-button" title="上一个"></div>
                <div class="indicator active" title="重置"></div>
                <div class="indicator nav-button" title="下一个"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="carousel.previousSlide()">⬆️ 上一个</button>
            <button class="btn" onclick="carousel.resetToDefault()">🔄 重置</button>
            <button class="btn" onclick="carousel.nextSlide()">⬇️ 下一个</button>
        </div>

        <div class="info">
            <h4>✨ 改进说明</h4>
            <p>
                <strong>之前：</strong> 使用淡入淡出 + 延迟的切换效果，视觉上比较突兀<br>
                <strong>现在：</strong> 使用连续的3D旋转动画，提供流畅自然的视觉体验
            </p>
        </div>
    </div>

    <script>
        class Demo3DCarousel {
            constructor() {
                this.container = document.querySelector('.lead-scroll-container');
                this.items = document.querySelectorAll('.lead-item');
                this.indicators = document.querySelectorAll('.indicator');
                this.currentIndex = 0;
                
                // 3D旋转相关属性
                this.rotationAngle = 90; // 每个项目之间的旋转角度
                this.currentRotation = 0; // 当前旋转角度
                this.translateZDistance = 120; // Z轴距离
                
                this.bindEvents();
                this.updateCylinderStates();
            }

            bindEvents() {
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        if (index === 0) {
                            this.previousSlide();
                        } else if (index === 1) {
                            this.resetToDefault();
                        } else if (index === 2) {
                            this.nextSlide();
                        }
                    });
                });
            }

            updateCylinderStates() {
                this.items.forEach((item, index) => {
                    // 清除所有状态类
                    item.classList.remove('active');
                    
                    // 计算每个项目的旋转角度
                    const rotationOffset = (index - this.currentIndex) * this.rotationAngle;
                    
                    // 应用3D变换
                    item.style.transform = `rotateX(${rotationOffset}deg) translateZ(${this.translateZDistance}px)`;
                    
                    // 根据位置设置透明度和z-index
                    const normalizedRotation = ((rotationOffset % 360) + 360) % 360;
                    
                    if (Math.abs(normalizedRotation) < 45 || Math.abs(normalizedRotation - 360) < 45) {
                        // 中心位置
                        item.style.opacity = '1';
                        item.style.zIndex = '3';
                        item.classList.add('active');
                    } else {
                        // 其他位置
                        item.style.opacity = '0.3';
                        item.style.zIndex = '1';
                    }
                });

                // 更新容器的整体旋转
                this.updateRotation();
            }

            updateRotation() {
                if (!this.container) return;
                // 计算旋转角度
                this.currentRotation = -this.currentIndex * this.rotationAngle;
                this.container.style.transform = `rotateX(${this.currentRotation}deg)`;
            }

            nextSlide() {
                const nextIndex = (this.currentIndex + 1) % this.items.length;
                this.goToSlide(nextIndex);
            }

            previousSlide() {
                const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
                this.goToSlide(prevIndex);
            }

            resetToDefault() {
                this.goToSlide(0);
            }

            goToSlide(index) {
                if (index < 0 || index >= this.items.length || index === this.currentIndex) {
                    return;
                }
                this.currentIndex = index;
                this.updateCylinderStates();
            }
        }

        // 初始化轮播
        let carousel;
        document.addEventListener('DOMContentLoaded', function() {
            carousel = new Demo3DCarousel();
        });
    </script>
</body>
</html>
