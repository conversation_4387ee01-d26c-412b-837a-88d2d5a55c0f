/**
 * 首页专用样式
 * Index Page Styles
 */

/* 禁用移动端双击放大功能 */
* {
    /* 禁用双击缩放 */
    touch-action: manipulation;
    /* 禁用文本选择（可选，防止意外选择） */
    -webkit-touch-callout: none;
    -webkit-user-select: none; 
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* 允许输入框和文本区域的文本选择 */
input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 允许特定元素的文本选择 */
.selectable-text, p, h1, h2, h3, h4, h5, h6, span, div.content {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 导航栏样式 */
.navbar {
    min-height: calc(56px - 5px + 2.5px) !important; /* Bootstrap默认56px，减少5px，再增加2.5px = 53.5px */
    padding-top: 0.35rem !important;
    padding-bottom: 0.35rem !important;
}

/* 主容器上边距调整 */
.container.my-4 {
    margin-top: 12px !important;
}

/* 轮播组件位置调整 */
.lead-carousel {
    margin-top: -10px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    height: 180px !important;
}

.navbar .search-input {
    width: 250px;
}

.search-input-mobile {
    width: 150px;
}

/* 移动端导航栏优化 */
.navbar-nav .nav-link {
    font-size: 0.9rem;
    white-space: nowrap;
}



/* 返回顶部按钮 */
.back-to-top {
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

/* 欢迎横幅 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px !important;
    position: relative;
    overflow: hidden;
}

/* 标题和Logo容器位置调整 */
.title-with-logo {
    margin-top: -28px !important;
    margin-bottom: 20px !important;
    padding-top: 30px !important;
    position: relative !important;
    z-index: 10 !important;
    transform: translateY(-16px) !important;
}

/* 标题文字位置调整 */
.title-with-logo .display-4 {
    margin-top: 8px !important;
    margin-bottom: 1rem !important;
}

/* Logo图片位置调整 */
.title-with-logo img {
    margin-top: -3px !important;
    margin-bottom: -5px !important;
}

/* 移动端专用调整 */
@media (max-width: 768px) {
    .title-with-logo {
        margin-top: 0px !important;
        padding-top: 0px !important;
        margin-bottom: 25px !important;
        transform: translateY(-25px) !important;
    }

    .title-with-logo .display-4 {
        margin-top: 3px !important;
        margin-bottom: 0.8rem !important;
    }

    .title-with-logo img {
        margin-top: -6px !important;
        margin-bottom: -8px !important;
    }
}

/* 小屏幕移动端进一步调整 */
@media (max-width: 480px) {
    .title-with-logo {
        margin-top: 0px !important;
        padding-top: 0px !important;
        margin-bottom: 20px !important;
        transform: translateY(-30px) !important;
    }

    .title-with-logo .display-4 {
        margin-top: 0px !important;
        margin-bottom: 0.5rem !important;
    }
    /* 调整Logo图片位置 */
    .title-with-logo img {
        margin-top: -2px !important;
        margin-bottom: -10px !important;
    }

    /* 手机端轮播组件位置调整 */
    .lead-carousel {
        margin-top: -16px !important;
    }
}

/* Lead容器 - 向上移动，更靠近标题 */
.lead-carousel {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 0.5rem 3rem 0.5rem 2rem;    /* 最小化上下内边距 */
    border: 2px solid rgba(255, 255, 255, 0.8);
    margin: 0.5rem auto 2rem auto;       /* 减少上边距，向上移动 */
    max-width: 600px;                /* 减小最大宽度 */
    position: relative;
    animation: fadeInUp 0.6s ease-out;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    overflow: visible; /* 允许指示器显示在外部 */
    height: 60px;                    /* 调整为合适的容器高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}



/* 3D滚筒内容容器 */
.lead-scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1000px;                    /* 添加透视效果 */
    transform-style: preserve-3d;
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 10px;                       /* 确保最小高度 */
    display: flex;                          /* 添加flex布局 */
    align-items: center;                    /* 垂直居中 */
    justify-content: center;                /* 水平居中 */
}

/* 3D滚筒项 - 紧凑布局 */
.lead-item {
    position: absolute;
    top: 50%;                        /* 垂直居中定位 */
    left: 0;                         /* 左对齐 */
    width: 100%;
    height: 40px;                    /* 进一步减少高度 */
    font-size: 0.9rem;               /* 进一步减小字体 */
    font-weight: 600;
    line-height: 40px;               /* 匹配高度 */
    color: #2d3748;
    letter-spacing: 0.05px;          /* 最小化字符间距 */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    backface-visibility: hidden;
    transform-origin: center center;
    margin-top: -20px;               /* 向上偏移一半高度实现居中 */
    /* 为轮播项添加平滑过渡动画 */
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-property: transform, opacity, background, box-shadow, height, width, left, z-index;
}

/* 3D滚筒项的不同位置状态 */
.lead-item:nth-child(1) {
    transform: rotateX(-120deg) translateZ(100px);  /* 位置1：上方 */
    opacity: 0.3;
    filter: none;
    z-index: 1;
}

.lead-item:nth-child(2) {
    transform: rotateX(0deg) translateZ(100px);     /* 位置2：中心（当前） */
    opacity: 1;
    filter: none;
    color: #000000;
    font-weight: 700;
    background: rgba(49, 130, 206, 0.05);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(49, 130, 206, 0.1);
    z-index: 3;
}

.lead-item:nth-child(3) {
    transform: rotateX(120deg) translateZ(100px);   /* 位置3：下方 */
    opacity: 0.3;
    filter: none;
    z-index: 1;
}

/* 动态状态类 */
.lead-item.cylinder-top {
    transform: rotateX(-60deg) translateZ(100px) translateY(-15px);  /* 添加向上偏移 */
    opacity: 0.2;
    filter: none;
    z-index: 1;
    align-items: flex-start;  /* 内容居上对齐 */
    padding-top: 5px;  /* 添加顶部内边距 */
    /* 添加内容过渡 */
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lead-item.cylinder-center {
    transform: rotateX(0deg) translateZ(100px);     /* 位置2：中心（当前） */
    opacity: 1;
    filter: none;
    color: #000000;
    font-weight: 700;
    background: rgba(49, 130, 206, 0.05);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(49, 130, 206, 0.1);
    z-index: 3;
    /* 调整尺寸适应字体 */
    height: 60px;                               /* 减少高度：40px -> 36px */
    line-height: 36px;                          /* 匹配高度 */
    width: 95%;                                 /* 减少宽度：100% -> 95% */
    left: 2.5%;                                 /* 居中对齐 */
    /* 字体完全居中显示 */
    display: flex;                              /* 使用flex布局 */
    align-items: center;                        /* 垂直居中 */
    justify-content: center;                    /* 水平居中 */
    text-align: center;                         /* 文本居中 */
    /* 添加内容过渡 */
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lead-item.cylinder-bottom {
    transform: rotateX(120deg) translateZ(100px);   /* 位置3：下方 */
    opacity: 0.2;
    filter: none;
    z-index: 1;
    align-items: flex-end;
    padding-bottom: 5px;
    /* 添加内容过渡 */
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Lead项强调文字 */
.lead-item strong {
    font-weight: 700;
    background: linear-gradient(135deg, #3182ce 0%, #2b6cb0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
    letter-spacing: 0.2px;
    transition: all 0.6s ease;
}

/* 激活项的显示效果 - 取消模糊，黑色字体 */
.lead-item.active {
    filter: none !important;               /* 完全取消模糊 */
    color: #000000 !important;             /* 纯黑色字体 */
    opacity: 1 !important;                 /* 完全不透明 */
    font-weight: 700 !important;           /* 加粗字体 */
    /* 调整尺寸适应字体 */
    height: 36px !important;               /* 减少高度：40px -> 36px */
    line-height: 36px !important;          /* 匹配高度 */
    width: 95% !important;                 /* 减少宽度：100% -> 95% */
    left: 2.5% !important;                 /* 居中对齐 */
    /* 字体完全居中显示 */
    display: flex !important;              /* 使用flex布局 */
    align-items: center !important;        /* 垂直居中 */
    justify-content: center !important;    /* 水平居中 */
    text-align: center !important;         /* 文本居中 */
}

/* 激活项的强调文字 */
.lead-item.active strong {
    background: linear-gradient(135deg, #1a365d 0%, #2b6cb0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 激活项和中心项的p标签居中调整 */
.lead-item.active p,
.lead-item.cylinder-center p {
    margin: 0 !important;                      /* 移除默认边距 */
    padding: 0 !important;                     /* 移除默认内边距 */
    line-height: inherit !important;           /* 继承父元素行高 */
    display: flex !important;                  /* 使用flex布局 */
    align-items: center !important;            /* 垂直居中 */
    justify-content: center !important;        /* 水平居中 */
    height: 100% !important;                   /* 占满父容器高度 */
    text-align: center !important;             /* 文本居中 */
}

/* Lead滚动容器悬停效果 */
.lead-carousel:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #3182ce;
    transform: translateY(-2px);
    /* 增加悬停效果 */
    box-shadow: 0 8px 25px rgba(49, 130, 206, 0.2);
    transition: all 0.3s ease;
}

/* 3D滚筒悬停效果 */
.lead-carousel:hover .lead-scroll-container {
    animation-play-state: paused;
}

/* 拖拽时的视觉反馈 */
.lead-carousel.dragging {
    user-select: none;
}

.lead-carousel.dragging .lead-scroll-container {
    transition: none; /* 拖拽时禁用过渡动画 */
}

/* 3D滚筒光影效果 */
.lead-item.cylinder-center::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(0, 0, 0, 0.05) 100%);
    border-radius: 8px;
    pointer-events: none;
}

/* 滚筒项的阴影效果 */
.lead-item.cylinder-top,
.lead-item.cylinder-bottom {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 防止容器空显的备用样式 */
.lead-scroll-container:empty::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-size: 0.9rem;
}

/* 确保至少有一个可见的lead-item */
.lead-scroll-container:not(:has(.lead-item:nth-child(1))) .lead-item:first-child,
.lead-scroll-container .lead-item:only-child {
    opacity: 1 !important;
    filter: none !important;
    color: #000000 !important;
    font-weight: 700 !important;
    transform: rotateX(0deg) translateZ(100px) !important;
}

/* 加载和错误状态样式 */
.loading-text, .error-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    padding: 1rem;
    font-size: 0.9rem;
    color: #666;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    white-space: nowrap;
}

.error-text {
    color: #e53e3e;
    background: rgba(229, 62, 62, 0.1);
}

/* 加载动画 */
.loading-text::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 8px;
    border: 2px solid #666;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}



/* 动画定义 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .lead-carousel {
        padding: 1.2rem 1.5rem;
        margin: 0.8rem auto 1.5rem auto; /* 减少上边距 */
        height: 150px;
        perspective: 800px; /* 平板端调整透视 */
    }

    .lead-item {
        height: 50px;
        font-size: 1rem;
        line-height: 50px;
        letter-spacing: 0.2px;
    }

    .lead-item:nth-child(1) {
        transform: rotateX(-50deg) translateZ(80px);
    }

    .lead-item:nth-child(2) {
        transform: rotateX(0deg) translateZ(80px);
    }

    .lead-item:nth-child(3) {
        transform: rotateX(50deg) translateZ(80px);
    }
    /*  */
    .lead-item.cylinder-top {
        transform: rotateX(-20deg) translateZ(80px) translateY(-12px);  /* 平板端向上偏移 */
        align-items: flex-start;  /* 内容居上对齐 */
        padding-top: 5px;  /* 添加顶部内边距 */
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .lead-item.cylinder-center {
        transform: rotateX(0deg) translateZ(80px);
        /* 平板端尺寸调整 */
        height: 46px;                           /* 减少高度：50px -> 46px */
        line-height: 46px;                      /* 匹配高度 */
        width: 95%;                             /* 减少宽度 */
        left: 2.5%;                             /* 居中对齐 */
        /* 字体完全居中显示 */
        display: flex;                          /* 使用flex布局 */
        align-items: center;                    /* 垂直居中 */
        justify-content: center;                /* 水平居中 */
        text-align: center;                     /* 文本居中 */
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* 平板端p标签居中调整 */
    .lead-item.cylinder-center p {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 23px !important;          /* 调整为分行显示的行高 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 100% !important;
        text-align: center !important;
        flex-direction: column !important;     /* 垂直排列 */
    }

    /* 平板端分行显示样式 */
    .lead-item.cylinder-center p br {
        display: block !important;             /* 确保换行符生效 */
        line-height: 0 !important;             /* 减少换行间距 */
        margin: 1px 0 !important;              /* 微调行间距 */
    }

    .lead-item.cylinder-bottom {
        transform: rotateX(50deg) translateZ(80px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}

@media (max-width: 576px) {
    .lead-carousel {
        padding: 1rem 1.2rem;
        margin: 0.5rem auto 1rem auto; /* 减少上边距 */
        height: 120px;
        perspective: 600px; /* 手机端进一步调整透视 */
    }

    .lead-item {
        height: 40px;
        font-size: 0.6rem;
        line-height: 40px;
        letter-spacing: 0.1px;
    }

    .lead-item:nth-child(1) {
        transform: rotateX(-45deg) translateZ(60px);
    }

    .lead-item:nth-child(2) {
        transform: rotateX(0deg) translateZ(60px);
    }

    .lead-item:nth-child(3) {
        transform: rotateX(45deg) translateZ(60px);
    }

    .lead-item.cylinder-top {
        transform: rotateX(-45deg) translateZ(60px) translateY(-10px);  /* 手机端向上偏移 */
        align-items: flex-start;  /* 内容居上对齐 */
        padding-top: 3px;  /* 手机端减少顶部内边距 */
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .lead-item.cylinder-center {
        transform: rotateX(0deg) translateZ(60px);
        /* 手机端尺寸调整 */
        height: 36px;                           /* 减少高度：40px -> 36px */
        line-height: 36px;                      /* 匹配高度 */
        width: 95%;                             /* 减少宽度 */
        left: 2.5%;                             /* 居中对齐 */
        /* 字体完全居中显示 */
        display: flex;                          /* 使用flex布局 */
        align-items: center;                    /* 垂直居中 */
        justify-content: center;                /* 水平居中 */
        text-align: center;                     /* 文本居中 */
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* 手机端p标签居中调整 */
    .lead-item.cylinder-center p {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 18px !important;          /* 调整为分行显示的行高 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 100% !important;
        text-align: center !important;
        flex-direction: column !important;     /* 垂直排列 */
    }

    /* 手机端分行显示样式 */
    .lead-item.cylinder-center p br {
        display: block !important;             /* 确保换行符生效 */
        line-height: 0 !important;             /* 减少换行间距 */
        margin: 1px 0 !important;              /* 微调行间距 */
    }

    .lead-item.cylinder-bottom {
        transform: rotateX(45deg) translateZ(60px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }


}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

/* 调整欢迎横幅中的row位置 */
.hero-section .row {
    margin-top: 0rem !important;
    position: relative;
    z-index: 2;
}

/* 移动端row位置调整 */
@media (max-width: 768px) {
    .hero-section .row {
        margin-top: 1rem !important;
    }
}

/* 视频卡片 */
.video-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.video-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .card-img-top {
    transform: scale(1.05);
}

.video-card .card-body {
    padding: 1.25rem;
}

.video-card .card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-card .card-text {
    color: #6c757d;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 视频元数据 */
.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.video-duration {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    position: absolute;
    bottom: 8px;
    right: 8px;
}

/* 加载更多按钮 */
.load-more-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* 统计信息 */
.stats-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-input-mobile {
        width: 120px;
    }



    .navbar {
        min-height: calc(48px - 5px + 2.5px) !important; /* 移动端缩小后再增加2.5px = 45.5px */
        padding-top: 0.25rem !important;
        padding-bottom: 0.25rem !important;
    }

    .navbar-nav .nav-link {
        font-size: 0.8rem;
        padding: 0.5rem 0.3rem !important;
    }

    .hero-section {
        padding: 0.5rem 1rem !important;
        margin-bottom: 2rem !important;
        margin-top: 0rem !important;
    }

    .video-card .card-img-top {
        height: 150px;
    }

    .back-to-top {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }

    .stats-section {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .search-input-mobile {
        width: 100px;
    }



    .navbar {
        min-height: calc(44px - 5px + 2.5px) !important; /* 小屏幕缩小后再增加2.5px = 41.5px */
        padding-top: 0.2rem !important;
        padding-bottom: 0.2rem !important;
    }

    .navbar-nav .nav-link {
        font-size: 0.75rem;
        padding: 0.4rem 0.2rem !important;
    }

    .video-card .card-body {
        padding: 1rem;
    }

    .hero-section {
        padding: 0.3rem 1rem !important;
        margin-top: 0rem !important;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 加载动画 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .video-card .card-title {
        color: #f7fafc;
    }
    
    .video-card .card-text {
        color: #a0aec0;
    }
    
    .stats-section {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .stat-label {
        color: #a0aec0;
    }
    
    .empty-state {
        color: #a0aec0;
    }
    
    .empty-state h3 {
        color: #e2e8f0;
    }
}

/* ==================== Lead Carousel 指示器样式 ==================== */

/* 右侧指示器容器 - 按图片位置设计 */
.lead-indicators {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 15;
}

/* 红色圆点指示器 - 按图片样式 */
.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(239, 68, 68, 0.6); /* 更鲜艳的红色 */
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 激活状态的指示器 - 中间指示器始终保持大圆点 */
.indicator.active {
    background: #ef4444; /* 鲜红色 */
    transform: scale(1.3);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.6);
    cursor: pointer; /* 中间指示器可点击，用于恢复默认值 */
}

/* 导航按钮指示器的悬停状态 - 只有上下指示器可悬停 */
.indicator.nav-button:hover {
    background: #dc2626;
    transform: scale(1.15);
    box-shadow: 0 2px 6px rgba(220, 38, 38, 0.5);
}

/* 确保中间指示器不受悬停影响 */
.indicator.active:hover {
    background: #ef4444;
    transform: scale(1.3);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.6);
}

/* 导航按钮样式 - 保持圆形外观 */
.indicator.nav-button {
    background: rgba(239, 68, 68, 0.6); /* 保持红色圆点样式 */
    width: 10px;                        /* 恢复原始尺寸 */
    height: 10px;
}

/* 导航按钮悬停效果已在上面定义，此处删除重复 */

/* ==================== 重置反馈效果 ==================== */

/* Lead Item 重置反馈动画 */
.lead-item.reset-feedback {
    animation: resetPulse 1s ease-in-out;
    background: rgba(76, 175, 80, 0.2) !important;
    border: 2px solid #4caf50 !important;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.4) !important;
}

/* 指示器重置反馈动画 */
.indicator.reset-feedback {
    animation: indicatorPulse 1s ease-in-out;
    background: #4caf50 !important;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6) !important;
    transform: scale(1.4) !important;
}

/* Lead Item 重置脉冲动画 */
@keyframes resetPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0.4);
    }
    25% {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 20px rgba(76, 175, 80, 0.6);
    }
    75% {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0.4);
    }
}

/* 指示器脉冲动画 */
@keyframes indicatorPulse {
    0% {
        transform: scale(1.3);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0.6);
    }
    25% {
        transform: scale(1.35);
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.7);
    }
    50% {
        transform: scale(1.4);
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.8);
    }
    75% {
        transform: scale(1.35);
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.7);
    }
    100% {
        transform: scale(1.3);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0.6);
    }
}

/* 为p标签内容添加与容器一致的过渡效果 */
.lead-item p {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 1;
}

/* 确保在状态切换时内容过渡与容器同步 */
.lead-item.cylinder-top p,
.lead-item.cylinder-center p,
.lead-item.cylinder-bottom p {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 响应式优化 - 平板端 */
@media (max-width: 768px) {
    .lead-item.cylinder-top {
        transform: rotateX(-20deg) translateZ(80px) translateY(-12px);
        align-items: flex-start;
        padding-top: 5px;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .lead-item.cylinder-center {
        transform: rotateX(0deg) translateZ(80px);
        height: 46px;
        line-height: 46px;
        width: 95%;
        left: 2.5%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .lead-item.cylinder-bottom {
        transform: rotateX(50deg) translateZ(80px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}

/* 响应式优化 - 手机端 */
@media (max-width: 480px) {
    .lead-item.cylinder-center {
        transform: rotateX(0deg) translateZ(60px);
        height: 36px;
        line-height: 36px;
        width: 95%;
        left: 2.5%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .lead-item.cylinder-bottom {
        transform: rotateX(45deg) translateZ(60px);
        transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
}



