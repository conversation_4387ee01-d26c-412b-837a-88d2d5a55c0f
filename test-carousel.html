<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="src/main/resources/static/css/index-style.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>轮播动画效果测试</h2>
        <p>点击指示器按钮测试动画效果</p>
        
        <!-- 轮播容器 -->
        <div class="lead-carousel" id="leadCarousel">
            <div class="lead-container">
                <div class="lead-item cylinder-center active">
                    <p>联系方式 1<br>电话: 123-456-7890</p>
                </div>
                <div class="lead-item cylinder-top">
                    <p>联系方式 2<br>邮箱: <EMAIL></p>
                </div>
                <div class="lead-item cylinder-bottom">
                    <p>联系方式 3<br>地址: 测试地址</p>
                </div>
            </div>
            
            <!-- 指示器 -->
            <div class="indicators-container">
                <div class="indicator nav-button"></div>
                <div class="indicator active"></div>
                <div class="indicator nav-button"></div>
            </div>
        </div>
    </div>

    <script>
        // 简化的轮播逻辑用于测试
        class TestCarousel {
            constructor() {
                this.container = document.querySelector('.lead-container');
                this.items = document.querySelectorAll('.lead-item');
                this.indicators = document.querySelectorAll('.indicator');
                this.currentIndex = 0;

                // 3D旋转相关属性
                this.rotationAngle = 120; // 每个项目之间的旋转角度
                this.currentRotation = 0; // 当前旋转角度
                this.translateZDistance = 100; // Z轴距离

                this.bindEvents();
            }

            bindEvents() {
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        if (index === 0) {
                            this.previousSlide();
                        } else if (index === 1) {
                            this.resetToDefault();
                        } else if (index === 2) {
                            this.nextSlide();
                        }
                    });
                });
            }

            updateCylinderStates() {
                this.items.forEach((item, index) => {
                    // 清除所有状态类
                    item.classList.remove('cylinder-top', 'cylinder-center', 'cylinder-bottom', 'active');

                    // 计算每个项目的旋转角度
                    const rotationOffset = (index - this.currentIndex) * this.rotationAngle;
                    const finalRotation = rotationOffset;

                    // 应用3D变换
                    item.style.transform = `rotateX(${finalRotation}deg) translateZ(${this.translateZDistance}px)`;

                    // 根据位置设置透明度和z-index
                    const normalizedRotation = ((finalRotation % 360) + 360) % 360;

                    if (Math.abs(normalizedRotation) < 30 || Math.abs(normalizedRotation - 360) < 30) {
                        // 中心位置
                        item.style.opacity = '1';
                        item.style.zIndex = '3';
                        item.classList.add('cylinder-center', 'active');
                    } else if ((normalizedRotation > 300 && normalizedRotation <= 360) || (normalizedRotation >= 0 && normalizedRotation < 90)) {
                        // 上方位置
                        item.style.opacity = '0.3';
                        item.style.zIndex = '1';
                        item.classList.add('cylinder-top');
                    } else if (normalizedRotation > 90 && normalizedRotation < 270) {
                        // 下方位置
                        item.style.opacity = '0.3';
                        item.style.zIndex = '1';
                        item.classList.add('cylinder-bottom');
                    } else {
                        // 其他位置（隐藏）
                        item.style.opacity = '0';
                        item.style.zIndex = '0';
                    }
                });

                // 更新容器的整体旋转
                this.updateRotation();
            }

            updateRotation() {
                if (!this.container) return;

                // 计算旋转角度
                this.currentRotation = -this.currentIndex * this.rotationAngle;
                this.container.style.transform = `rotateX(${this.currentRotation}deg)`;
            }

            nextSlide() {
                const nextIndex = (this.currentIndex + 1) % this.items.length;
                this.goToSlide(nextIndex);
            }

            previousSlide() {
                const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
                this.goToSlide(prevIndex);
            }

            resetToDefault() {
                this.goToSlide(0);
            }

            goToSlide(index) {
                if (index < 0 || index >= this.items.length || index === this.currentIndex) {
                    return;
                }

                this.currentIndex = index;
                this.updateCylinderStates();
            }
        }

        // 初始化轮播
        document.addEventListener('DOMContentLoaded', function() {
            new TestCarousel();
        });
    </script>
</body>
</html>
