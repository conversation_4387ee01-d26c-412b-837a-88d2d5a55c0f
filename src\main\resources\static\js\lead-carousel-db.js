/**
 * Lead 3D滚筒组件 - 数据库驱动版本
 * 实现从数据库动态获取联系信息的3D滚筒选择效果
 * <AUTHOR>
 * @version 4.0.0
 */

class LeadCarouselDB {
    constructor(options = {}) {
        this.container = document.querySelector('.lead-scroll-container');
        this.indicatorsContainer = document.querySelector('.lead-indicators');
        this.currentIndex = 0;
        this.defaultIndex = 0; // 默认项目索引
        this.items = [];
        this.contactData = [];
        this.isDragging = false;
        this.isLoading = false;
        this.defaultDetected = false; // 防止重复检测默认项目
        this.isInitialized = false; // 防止重复初始化
        this.abortControllers = []; // 存储所有的 AbortController

        // 静态默认数据，用于快速加载失败时的备用
        this.defaultContactData = [
            {
                id: 1,
                contactName: '林佳',
                phone: '18722880704',
                wechat: 'linjia2024',
                douyin: 'huanglinjia',
                douyinNickname: '皮皮管理',
                contactType: '主管',
                isDefault: true,
                sortOrder: 1
            }
        ];
        
        // 配置选项（移动端优化）
        const isMobile = this.isMobileDevice();
        this.options = {
            autoRotate: false,
            rotateInterval: 5000,
            apiUrl: '/api/lead-contacts/carousel',
            allApiUrl: '/api/lead-contacts/all', // 🎯 备用API获取全部数据
            limit: 0, // 🎯 设置为0获取全部数据
            retryAttempts: isMobile ? 3 : 2, // 📱 移动端增加重试次数
            retryDelay: isMobile ? 500 : 200,  // 📱 移动端增加重试延迟
            showLoadingText: false, // 🚀 不显示加载文本
            timeout: isMobile ? 5000 : 3000,    // 📱 移动端增加超时时间
            ...options
        };

        if (!this.container) {
            console.warn('Lead carousel: Container not found');
            return;
        }

        // 不在构造函数中自动初始化，由外部调用 init() 方法
    }

    /**
     * 初始化组件
     */
    async init() {
        // 防止重复初始化
        if (this.isInitialized) {
            console.log('⚠️ 轮播组件已经初始化，跳过重复初始化');
            return;
        }

        try {
            // 🎯 优先加载数据库数据
            const isMobile = this.isMobileDevice();

            try {
                const realData = await this.loadContactData();
                if (realData && realData.length > 0) {
                    this.contactData = realData;
                    this.defaultDetected = false; // 重置检测标志
                    console.log(`✅ 轮播组件初始化完成: ${realData.length} 条联系信息`);
                } else {
                    throw new Error('数据库返回空数据');
                }
            } catch (error) {
                // 🔄 数据库加载失败时使用备用数据
                console.warn('⚠️ 数据库加载失败，使用备用数据');

                // 📱 移动端特殊处理：尝试延迟重试一次
                if (isMobile && !this.mobileRetryAttempted) {
                    this.mobileRetryAttempted = true;

                    setTimeout(async () => {
                        try {
                            const retryData = await this.loadContactData();
                            if (retryData && retryData.length > 0) {
                                this.contactData = retryData;
                                this.defaultDetected = false; // 重置检测标志
                                this.renderCarousel();
                                console.log(`✅ 移动端重试成功: ${retryData.length} 条联系信息`);
                                return;
                            }
                        } catch (retryError) {
                            // 静默处理重试失败
                        }
                    }, 2000);
                }

                this.contactData = [...this.defaultContactData];
                this.defaultDetected = false; // 重置检测标志
            }

            // 🎨 渲染轮播组件
            this.renderCarousel();
            this.bindEvents();

            // 标记为已初始化
            this.isInitialized = true;

        } catch (error) {
            console.error('❌ 轮播组件初始化失败:', error);

            // 📱 移动端特殊错误处理
            if (this.isMobileDevice()) {
                this.handleMobileError(error);
            } else {
                this.showError('加载联系信息失败');
            }
        }
    }



    /**
     * 从数据库加载联系信息
     */
    async loadContactData() {
        this.isLoading = true;
        const isMobile = this.isMobileDevice();

        for (let attempt = 1; attempt <= this.options.retryAttempts; attempt++) {
            let timeoutId = null;
            let controller = null;

            try {
                // 📱 移动端网络状态检查
                if (isMobile && navigator.onLine === false) {
                    throw new Error('移动端网络离线');
                }

                // 🚀 添加超时控制
                controller = new AbortController();
                this.abortControllers.push(controller); // 添加到管理数组

                timeoutId = setTimeout(() => {
                    try {
                        if (controller) {
                            controller.abort();
                        }
                    } catch (abortError) {
                        // 静默处理abort错误
                    }
                }, this.options.timeout);

                // 🎯 优先使用 /all 接口获取全部数据
                let url = this.options.limit > 0
                    ? `${this.options.apiUrl}?limit=${this.options.limit}`
                    : this.options.allApiUrl;

                // 📱 添加时间戳防止缓存
                const timestamp = Date.now();
                url += (url.includes('?') ? '&' : '?') + `_t=${timestamp}`;

                // 📱 移动端额外添加随机数
                if (isMobile) {
                    url += `&_r=${Math.random().toString(36).substr(2, 9)}`;
                }

                if (attempt > 1) {
                    console.log(`🌐 第${attempt}次重试请求数据`);
                }

                const response = await fetch(url, {
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        // 📱 移动端强制刷新缓存
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    // 📱 添加随机参数防止缓存
                    cache: 'no-store'
                });

                // 确保清除超时定时器
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success && result.data && result.data.length > 0) {
                    // 设置默认当前索引
                    if (result.defaultContactId) {
                        const defaultIndex = result.data.findIndex(contact => contact.id === result.defaultContactId);
                        if (defaultIndex !== -1) {
                            this.currentIndex = defaultIndex;
                        }
                    }

                    this.isLoading = false;
                    return result.data;
                } else {
                    throw new Error(`无效的API响应: success=${result.success}, dataLength=${result.data?.length || 0}`);
                }

            } catch (error) {
                // 确保清除超时定时器（如果存在）
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }

                // 特殊处理 AbortError
                if (error.name === 'AbortError') {
                    if (attempt === this.options.retryAttempts) {
                        this.isLoading = false;
                        console.error(`❌ 请求超时，所有重试已用完`);
                        throw new Error('请求超时');
                    }
                } else if (attempt === this.options.retryAttempts) {
                    this.isLoading = false;
                    console.error(`❌ 数据加载失败:`, error.message);
                    throw error;
                }

                if (attempt < this.options.retryAttempts) {
                    await this.delay(this.options.retryDelay);
                }
            }
        }

        this.isLoading = false;
    }



    /**
     * 渲染轮播组件
     */
    renderCarousel() {
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建联系信息项
        this.contactData.forEach((contact, index) => {
            const item = this.createContactItem(contact, index);
            this.container.appendChild(item);
        });
        
        // 更新items引用
        this.items = this.container.querySelectorAll('.lead-item');

        // 检测默认项目
        this.detectDefaultItem();

        // 渲染指示器
        this.renderIndicators();

        // 设置初始状态
        this.updateCylinderStates();
    }

    /**
     * 创建联系信息项DOM元素
     */
    createContactItem(contact, index) {
        const item = document.createElement('div');
        item.className = 'lead-item';
        item.dataset.contactId = contact.id;
        item.dataset.index = index;
        
        // 生成显示文本
        const displayText = this.generateDisplayText(contact);
        
        item.innerHTML = `<p>${displayText}</p>`;
        
        return item;
    }

    /**
     * 生成联系信息显示文本
     */
    generateDisplayText(contact) {
        let firstLine = '';
        let secondLine = '';

        // 第一行：微信/电话部分
        if (contact.wechat && contact.phone) {
            firstLine = `微信/电话：${contact.phone}【${contact.contactName}】`;
        } else if (contact.phone) {
            firstLine = `电话：${contact.phone}【${contact.contactName}】`;
        } else if (contact.wechat) {
            firstLine = `微信：${contact.wechat}【${contact.contactName}】`;
        }

        // 第二行：抖音部分
        if (contact.douyin) {
            secondLine = '抖音号：';
            if (contact.douyinNickname) {
                // 特殊处理：如果抖音昵称包含皮皮管理，按指定格式显示
                if (contact.douyinNickname.includes('皮皮管理')) {
                    secondLine += `黄林佳：皮皮管理`;
                } else {
                    secondLine += contact.douyinNickname;
                }
            } else {
                secondLine += contact.douyin;
            }
        }

        // 检测设备类型并返回相应格式
        const isMobile = this.isMobileDevice();

        if (isMobile && firstLine && secondLine) {
            // 手机端：分两行显示
            return `${firstLine}<br>${secondLine}`;
        } else {
            // 桌面端：单行显示（保持原有格式）
            let text = firstLine;
            if (secondLine) {
                text += `&nbsp;${secondLine}`;
            }
            return text;
        }
    }

    /**
     * 检测是否为移动设备
     */
    isMobileDevice() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 📱 移动端数据加载测试
     */
    async testMobileDataLoading() {
        if (!this.isMobileDevice()) {
            return;
        }

        // 静默测试，不输出日志
        try {
            const data = await this.loadContactData();
            return data;
        } catch (error) {
            return null;
        }
    }

    /**
     * 📱 移动端错误恢复机制
     */
    handleMobileError(error) {
        // 静默尝试重新初始化
        setTimeout(async () => {
            try {
                // 检查容器是否还存在
                if (!this.container || !document.contains(this.container)) {
                    this.container = document.querySelector('.lead-scroll-container');
                    this.indicatorsContainer = document.querySelector('.lead-indicators');
                }

                // 如果数据为空，重新加载
                if (!this.contactData || this.contactData.length === 0) {
                    const realData = await this.loadContactData();
                    if (realData && realData.length > 0) {
                        this.contactData = realData;
                    } else {
                        this.contactData = [...this.defaultContactData];
                    }
                }

                // 重新渲染
                this.renderCarousel();
                this.updateLayout();

            } catch (recoveryError) {
                // 静默处理恢复失败
            }
        }, 2000);
    }

    /**
     * 渲染指示器 - 固定样式
     */
    renderIndicators() {
        if (!this.indicatorsContainer) return;

        this.indicatorsContainer.innerHTML = '';

        // 固定3个指示器：上、中、下，样式不变
        for (let i = 0; i < 3; i++) {
            const indicator = document.createElement('span');
            indicator.className = 'indicator';
            indicator.dataset.index = i;

            if (i === 0) {
                indicator.classList.add('nav-button'); // 上方按钮（小圆点）
                indicator.title = '上一个联系方式';
            } else if (i === 1) {
                indicator.classList.add('active'); // 中间激活（大圆点，始终保持）
                indicator.title = '点击恢复默认联系方式';
            } else if (i === 2) {
                indicator.classList.add('nav-button'); // 下方按钮（小圆点）
                indicator.title = '下一个联系方式';
            }

            this.indicatorsContainer.appendChild(indicator);
        }
    }

    /**
     * 更新3D滚筒状态 - 循环布局（带内容过渡动画）
     */
    updateCylinderStates() {
        // 先让所有p标签内容淡出
        this.items.forEach(item => {
            const p = item.querySelector('p');
            if (p) {
                p.style.opacity = '0';
            }
        });

        // 延迟应用新状态和内容
        setTimeout(() => {
            this.items.forEach((item, index) => {
                // 清除所有状态类
                item.classList.remove('cylinder-top', 'cylinder-center', 'cylinder-bottom', 'active');
                
                // 重置内联样式
                item.style.opacity = '';
                item.style.transform = '';

                // 计算循环位置：1=top, 2=center, 3=bottom
                let position;
                if (index === this.currentIndex) {
                    position = 2; // 当前项在中心位置
                    item.classList.add('active'); // 添加active类
                } else if (index === (this.currentIndex - 1 + this.items.length) % this.items.length) {
                    position = 1; // 上一项在上方位置
                } else if (index === (this.currentIndex + 1) % this.items.length) {
                    position = 3; // 下一项在下方位置
                } else {
                    // 其他项目隐藏（如果有超过3个项目）
                    item.style.opacity = '0';
                    item.style.transform = 'rotateX(180deg) translateZ(100px)';
                    return;
                }

                // 应用对应的状态类
                if (position === 1) {
                    item.classList.add('cylinder-top');
                } else if (position === 2) {
                    item.classList.add('cylinder-center');
                } else if (position === 3) {
                    item.classList.add('cylinder-bottom');
                }
            });

            // 再让内容淡入
            setTimeout(() => {
                this.items.forEach(item => {
                    const p = item.querySelector('p');
                    if (p) {
                        p.style.opacity = '1';
                    }
                });
            }, 100);

            // 同时更新指示器状态
            this.updateIndicators();
        }, 150);
    }

    /**
     * 更新指示器状态 - 保持固定样式
     */
    updateIndicators() {
        // 指示器保持固定样式，不根据当前索引改变
        // 中间指示器始终保持激活状态（大圆点）
        // 上下指示器始终保持普通状态（小圆点）

        const indicators = this.indicatorsContainer?.querySelectorAll('.indicator');
        if (!indicators) return;

        // 确保样式保持不变
        indicators.forEach((indicator, index) => {
            if (index === 1) {
                // 中间指示器始终激活
                indicator.classList.add('active');
                indicator.classList.remove('nav-button');
            } else {
                // 上下指示器始终为导航按钮样式
                indicator.classList.remove('active');
                indicator.classList.add('nav-button');
            }
        });
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 指示器点击事件
        const indicators = this.indicatorsContainer?.querySelectorAll('.indicator');
        indicators?.forEach((indicator, index) => {
            indicator.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log(`Indicator ${index} clicked`);

                if (index === 0) {
                    console.log('Previous slide triggered');
                    this.previousSlide();
                } else if (index === 1) {
                    // 中间的点：恢复显示默认值
                    console.log('Reset to default triggered');
                    this.resetToDefault();
                } else if (index === 2) {
                    console.log('Next slide triggered');
                    this.nextSlide();
                }
            });
        });

        console.log(`Bound events to ${indicators?.length || 0} indicators`);

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.previousSlide();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.nextSlide();
            }
        });

        // 触摸滑动支持
        this.addTouchSupport();
        
        // 监听窗口大小变化，重新渲染文本（防抖处理）
        this.handleResize = this.debounce(() => {
            console.log('📱 窗口大小变化，重新渲染文本');
            this.refreshDisplayText();
        }, 300);

        window.addEventListener('resize', this.handleResize);

        // 📱 移动端特有事件处理
        if (this.isMobileDevice()) {
            // 监听屏幕方向变化
            this.handleOrientationChange = this.debounce(() => {
                console.log('📱 屏幕方向变化，重新渲染');
                setTimeout(() => {
                    this.refreshDisplayText();
                    this.updateLayout();
                }, 100);
            }, 500);

            window.addEventListener('orientationchange', this.handleOrientationChange);




        }
    }

    /**
     * 添加触摸滑动支持
     */
    addTouchSupport() {
        let startY = 0;
        let currentY = 0;

        this.container.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
            this.isDragging = true;
        });

        this.container.addEventListener('touchmove', (e) => {
            if (!this.isDragging) return;
            e.preventDefault();
            currentY = e.touches[0].clientY;
        });

        this.container.addEventListener('touchend', () => {
            if (!this.isDragging) return;
            this.isDragging = false;

            const deltaY = currentY - startY;
            if (Math.abs(deltaY) > 30) {
                if (deltaY > 0) {
                    this.previousSlide();
                } else {
                    this.nextSlide();
                }
            }
        });
    }

    /**
     * 下一张幻灯片
     */
    nextSlide() {
        const nextIndex = (this.currentIndex + 1) % this.items.length;
        this.goToSlide(nextIndex);
    }

    /**
     * 上一张幻灯片
     */
    previousSlide() {
        const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
        this.goToSlide(prevIndex);
    }

    /**
     * 跳转到指定幻灯片
     */
    goToSlide(index) {
        if (index < 0 || index >= this.items.length || index === this.currentIndex) {
            return;
        }

        this.currentIndex = index;
        this.updateCylinderStates();
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        if (!this.options.showLoadingText) return;
        
        this.container.innerHTML = '<div class="loading-text">加载联系信息中...</div>';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingText = this.container.querySelector('.loading-text');
        if (loadingText) {
            loadingText.remove();
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.container.innerHTML = `<div class="error-text">${message}</div>`;
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 更新布局（移动端适配）
     */
    updateLayout() {
        if (!this.container || !this.items.length) return;

        console.log('🔄 更新布局适配');

        // 重新检测设备类型
        const isMobile = this.isMobileDevice();

        // 更新容器类名
        this.container.classList.toggle('mobile-layout', isMobile);

        // 重新应用当前状态的样式
        this.updateItemStates();

        // 强制重绘
        this.container.offsetHeight;
    }





    /**
     * 获取当前联系信息
     */
    getCurrentContact() {
        return this.contactData[this.currentIndex];
    }

    /**
     * 检测默认项目
     * 查找具有默认标识的项目，如果没有则使用第一个项目
     */
    detectDefaultItem() {
        // 防止重复检测
        if (this.defaultDetected) {
            return;
        }

        // 方法1：查找ID为1的默认联系人
        for (let i = 0; i < this.contactData.length; i++) {
            const contact = this.contactData[i];
            if (contact.id === 1) {
                this.defaultIndex = i;
                this.defaultDetected = true;
                return;
            }
        }

        // 方法2：查找具有 isDefault 标记的联系信息
        for (let i = 0; i < this.contactData.length; i++) {
            const contact = this.contactData[i];
            if (contact.isDefault || contact.is_default) {
                this.defaultIndex = i;
                this.defaultDetected = true;
                return;
            }
        }

        // 方法3：查找类型为"主要"或"默认"的联系信息
        for (let i = 0; i < this.contactData.length; i++) {
            const contact = this.contactData[i];
            const type = (contact.contactType || contact.contact_type || '').toLowerCase();
            if (type.includes('主要') || type.includes('默认') || type.includes('推荐')) {
                this.defaultIndex = i;
                this.defaultDetected = true;
                return;
            }
        }

        // 默认使用第一个项目
        this.defaultIndex = 0;
        this.defaultDetected = true;
    }

    /**
     * 恢复到默认值
     * 点击中间指示器时调用
     */
    resetToDefault() {
        if (this.contactData.length === 0) {
            console.warn('No contact data available for reset');
            return;
        }

        if (this.currentIndex === this.defaultIndex) {
            // 如果当前已经是默认项目，添加视觉反馈
            this.showResetFeedback();
            console.log('Already at default contact');
        } else {
            // 切换到默认项目
            this.goToSlide(this.defaultIndex);
            this.showResetFeedback();
            console.log('Reset to default contact at index:', this.defaultIndex);
        }
    }

    /**
     * 显示重置反馈效果
     * 为用户提供视觉反馈，表明已经恢复到默认值
     */
    showResetFeedback() {
        const currentItem = this.items[this.currentIndex];
        if (!currentItem) return;

        // 添加重置反馈类
        currentItem.classList.add('reset-feedback');

        // 为中间指示器添加反馈效果
        const middleIndicator = this.indicatorsContainer?.querySelector('.indicator.active');
        if (middleIndicator) {
            middleIndicator.classList.add('reset-feedback');
        }

        // 1秒后移除反馈效果
        setTimeout(() => {
            currentItem.classList.remove('reset-feedback');
            if (middleIndicator) {
                middleIndicator.classList.remove('reset-feedback');
            }
        }, 1000);
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 中止所有进行中的请求
        if (this.abortControllers && this.abortControllers.length > 0) {
            this.abortControllers.forEach(controller => {
                try {
                    if (controller && !controller.signal.aborted) {
                        controller.abort();
                    }
                } catch (error) {
                    // 静默处理abort错误
                }
            });
            this.abortControllers = [];
        }

        // 移除事件监听器
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }

        if (this.handleResize) {
            window.removeEventListener('resize', this.handleResize);
        }

        if (this.handleOrientationChange) {
            window.removeEventListener('orientationchange', this.handleOrientationChange);
        }

        // 清除定时器
        if (this.resizeTimer) {
            clearTimeout(this.resizeTimer);
        }

        // 清空容器
        if (this.container) {
            this.container.innerHTML = '';
        }

        // 清空指示器
        if (this.indicatorsContainer) {
            this.indicatorsContainer.innerHTML = '';
        }

        // 清空数据
        this.contactData = [];
        this.items = [];

        // 重置状态
        this.isInitialized = false;
        this.defaultDetected = false;
        this.isLoading = false;
    }

    /**
     * 刷新显示文本（用于移动端适配）
     */
    refreshDisplayText() {
        this.items.forEach((item, index) => {
            const contact = this.contactData[index];
            if (contact) {
                const displayText = this.generateDisplayText(contact);
                const p = item.querySelector('p');
                if (p) {
                    p.innerHTML = displayText;
                }
            }
        });
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 检查是否已经有轮播实例
    if (window.leadCarousel) {
        console.log('⚠️ 轮播组件实例已存在，跳过重复创建');
        return;
    }

    // 检查是否存在轮播容器
    if (document.querySelector('.lead-scroll-container')) {
        window.leadCarousel = new LeadCarouselDB();

        try {
            // 📱 移动端先进行数据加载测试
            if (window.leadCarousel.isMobileDevice()) {
                await window.leadCarousel.testMobileDataLoading();
            }

            // 初始化轮播组件
            await window.leadCarousel.init();

        } catch (error) {
            console.error('❌ 轮播组件初始化失败:', error);
        }
    }
});

// 📱 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.leadCarousel) {
        window.leadCarousel.destroy();
    }
});













