<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6ea5dafc-7510-413b-9b34-3fdfd8b51cdb" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Program Files\Java_tools\maven\apache-maven-3.9.1\apache-maven-3.9.1" />
        <option name="localRepository" value="D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Program Files\Java_tools\maven\apache-maven-3.9.1\apache-maven-3.9.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30NEtsApxEqL2NFZmHYWozXK9fR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.video-player [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.video-player [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.VideoPlayerApplication.executor&quot;: &quot;Run&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/admin/desktop/video-player-project/database&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\admin\desktop\video-player-project\database" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="video-player-project" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="video-player-project" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="VideoPlayerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="video-player" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.videoplayer.VideoPlayerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6ea5dafc-7510-413b-9b34-3fdfd8b51cdb" name="Changes" comment="" />
      <created>1753459847605</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753459847605</updated>
      <workItem from="1753459848773" duration="9620000" />
      <workItem from="1753472625790" duration="334000" />
      <workItem from="1753473069037" duration="2270000" />
      <workItem from="1753476575950" duration="26753000" />
      <workItem from="1753569301377" duration="1597000" />
      <workItem from="1753570950240" duration="1415000" />
      <workItem from="1753572436978" duration="756000" />
      <workItem from="1753573668424" duration="1196000" />
      <workItem from="1753575535084" duration="5766000" />
      <workItem from="1753582964696" duration="19809000" />
      <workItem from="1753710601124" duration="774000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>